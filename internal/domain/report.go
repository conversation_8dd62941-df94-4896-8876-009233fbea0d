package domain

type Report struct {
	AIReadinessScore int    `json:"aiReadinessScore"`
	ScoreMessage     string `json:"scoreMessage"`
	ProjectedImpact  struct {
		Type  string `json:"type"`
		Value string `json:"value"`
	} `json:"projectedImpact"`
	WasteAreas    []string `json:"wasteAreas"`
	Opportunities []struct {
		Title       string `json:"title"`
		Description string `json:"description"`
	} `json:"opportunities"`
}
